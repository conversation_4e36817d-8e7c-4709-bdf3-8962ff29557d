const express = require("express");
const path = require("path");
const fs = require("fs");
const File = require("../models/File");

const router = express.Router();

// GET /api/download/:fileId/info - Get file information without downloading
router.get("/:fileId/info", async (req, res) => {
  try {
    const { fileId } = req.params;

    const file = await File.findOne({ fileId: fileId });

    if (!file) {
      return res.status(404).json({
        success: false,
        message: "File not found",
      });
    }

    res.status(200).json({
      success: true,
      file: {
        id: file.fileId,
        originalName: file.originalName,
        size: file.size,
        mimetype: file.mimetype,
        uploadDate: file.uploadDate,
        downloadCount: file.downloadCount,
      },
    });
  } catch (error) {
    console.error("File info error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    });
  }
});

// GET /api/download/:fileId - Handle file download
router.get("/:fileId", async (req, res) => {
  try {
    const { fileId } = req.params;

    // Find file in database by fileId
    const file = await File.findOne({ fileId: fileId });

    if (!file) {
      return res.status(404).json({
        success: false,
        message: "File not found",
      });
    }

    // Check if file exists on server
    const filePath = path.resolve(file.filePath);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({
        success: false,
        message: "File not found on server",
      });
    }

    // Increment download count
    file.downloadCount += 1;
    await file.save();

    // Set appropriate headers for file download
    res.setHeader(
      "Content-Disposition",
      `attachment; filename="${file.originalName}"`
    );
    res.setHeader("Content-Type", file.mimetype);
    res.setHeader("Content-Length", file.size);

    // Stream the file to client
    const fileStream = fs.createReadStream(filePath);
    fileStream.pipe(res);

    // Handle stream errors
    fileStream.on("error", (error) => {
      console.error("File stream error:", error);
      if (!res.headersSent) {
        res.status(500).json({
          success: false,
          message: "Error streaming file",
        });
      }
    });
  } catch (error) {
    console.error("Download error:", error);
    res.status(500).json({
      success: false,
      message: "Server error during file download",
      error: error.message,
    });
  }
});

// GET /api/download/batch/:batchId - Get batch file information
router.get("/batch/:batchId", async (req, res) => {
  try {
    const { batchId } = req.params;

    const files = await File.find({ batchId: batchId });

    if (!files || files.length === 0) {
      return res.status(404).json({
        success: false,
        message: "Batch not found",
      });
    }

    const fileList = files.map((file) => ({
      id: file.fileId,
      originalName: file.originalName,
      size: file.size,
      mimetype: file.mimetype,
      uploadDate: file.uploadDate,
      downloadCount: file.downloadCount,
      downloadLink: `${req.protocol}://${req.get("host")}/api/download/${
        file.fileId
      }`,
    }));

    res.status(200).json({
      success: true,
      batchId: batchId,
      files: fileList,
      totalFiles: files.length,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      description: files[0].description || null,
    });
  } catch (error) {
    console.error("Batch info error:", error);
    res.status(500).json({
      success: false,
      message: "Server error",
      error: error.message,
    });
  }
});

module.exports = router;
