const express = require("express");
const { v4: uuidv4 } = require("uuid");
const QRCode = require("qrcode");
const sharp = require("sharp");
const path = require("path");
const fs = require("fs");
const archiver = require("archiver");
const File = require("../models/File");
const {
  uploadSingle,
  uploadMultiple,
  checkFileSize,
} = require("../middleware/upload");

const router = express.Router();

// Helper function to generate thumbnail for images
const generateThumbnail = async (filePath, mimetype) => {
  try {
    if (!mimetype.startsWith("image/")) return null;

    const thumbnailPath = filePath
      .replace("/files/", "/thumbnails/")
      .replace(path.extname(filePath), "_thumb.jpg");

    await sharp(filePath)
      .resize(300, 300, { fit: "inside", withoutEnlargement: true })
      .jpeg({ quality: 80 })
      .toFile(thumbnailPath);

    return thumbnailPath;
  } catch (error) {
    console.error("Thumbnail generation error:", error);
    return null;
  }
};

// Helper function to generate QR code
const generateQRCode = async (downloadLink) => {
  try {
    const qrCodeDataURL = await QRCode.toDataURL(downloadLink, {
      width: 200,
      margin: 2,
      color: {
        dark: "#000000",
        light: "#FFFFFF",
      },
    });
    return qrCodeDataURL;
  } catch (error) {
    console.error("QR code generation error:", error);
    return null;
  }
};

// POST /api/upload/single - Handle single file upload
router.post(
  "/single",
  uploadSingle.single("file"),
  checkFileSize,
  async (req, res) => {
    try {
      // Check if file was uploaded
      if (!req.file) {
        return res.status(400).json({
          success: false,
          message: "No file uploaded",
        });
      }

      // Generate unique ID for the file (used in shareable link)
      const fileId = uuidv4();

      // Generate download link
      const downloadLink = `${req.protocol}://${req.get(
        "host"
      )}/api/download/${fileId}`;

      // Generate QR code for the download link
      const qrCode = await generateQRCode(downloadLink);

      // Generate thumbnail for images
      const thumbnail = await generateThumbnail(
        req.file.path,
        req.file.mimetype
      );

      // Parse additional metadata from request body
      const { description, tags, expiresIn, isPasswordProtected, password } =
        req.body;

      // Calculate expiration date
      let expiresAt = null;
      if (expiresIn && parseInt(expiresIn) > 0) {
        expiresAt = new Date(
          Date.now() + parseInt(expiresIn) * 24 * 60 * 60 * 1000
        ); // days to milliseconds
      }

      // Create new file document in MongoDB
      const newFile = new File({
        fileId: fileId,
        owner: null, // No authentication, so no owner
        originalName: req.file.originalname,
        filename: req.file.filename,
        filePath: req.file.path,
        size: req.file.size,
        mimetype: req.file.mimetype,
        description: description || "",
        tags: tags
          ? tags
              .split(",")
              .map((tag) => tag.trim())
              .filter((tag) => tag)
          : [],
        expiresAt: expiresAt,
        isPasswordProtected: isPasswordProtected === "true",
        password: isPasswordProtected === "true" ? password : null,
        qrCode: qrCode,
        thumbnail: thumbnail,
      });

      // Save file metadata to database
      await newFile.save();

      // Send success response with file info and download link
      res.status(200).json({
        success: true,
        message: "File uploaded successfully",
        file: {
          id: fileId,
          originalName: req.file.originalname,
          size: req.file.size,
          mimetype: req.file.mimetype,
          category: newFile.category,
          description: newFile.description,
          tags: newFile.tags,
          downloadLink: downloadLink,
          qrCode: qrCode,
          thumbnail: thumbnail,
          expiresAt: expiresAt,
          isPasswordProtected: newFile.isPasswordProtected,
          uploadDate: newFile.uploadDate,
        },
      });
    } catch (error) {
      console.error("Upload error:", error);

      // Clean up uploaded file if database save failed
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
      }

      res.status(500).json({
        success: false,
        message: "Server error during file upload",
        error: error.message,
      });
    }
  }
);

// POST /api/upload/bulk - Handle multiple file upload (UNIQUE FEATURE)
router.post(
  "/bulk",
  uploadMultiple.array("files", 20),
  checkFileSize,
  async (req, res) => {
    try {
      // Check if files were uploaded
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({
          success: false,
          message: "No files uploaded",
        });
      }

      // Generate batch ID for grouping files
      const batchId = uuidv4();
      const uploadedFiles = [];
      const errors = [];

      // Parse additional metadata
      const { description, tags, expiresIn, createZip } = req.body;

      // Calculate expiration date
      let expiresAt = null;
      if (expiresIn && parseInt(expiresIn) > 0) {
        expiresAt = new Date(
          Date.now() + parseInt(expiresIn) * 24 * 60 * 60 * 1000
        );
      }

      // Process each file
      for (const file of req.files) {
        try {
          const fileId = uuidv4();
          const downloadLink = `${req.protocol}://${req.get(
            "host"
          )}/api/download/${fileId}`;

          // Generate QR code and thumbnail
          const qrCode = await generateQRCode(downloadLink);
          const thumbnail = await generateThumbnail(file.path, file.mimetype);

          // Create file document
          const newFile = new File({
            fileId: fileId,
            owner: null, // No authentication, so no owner
            originalName: file.originalname,
            filename: file.filename,
            filePath: file.path,
            size: file.size,
            mimetype: file.mimetype,
            description: description || "",
            tags: tags
              ? tags
                  .split(",")
                  .map((tag) => tag.trim())
                  .filter((tag) => tag)
              : [],
            expiresAt: expiresAt,
            batchId: batchId,
            qrCode: qrCode,
            thumbnail: thumbnail,
          });

          await newFile.save();

          uploadedFiles.push({
            id: fileId,
            originalName: file.originalname,
            size: file.size,
            mimetype: file.mimetype,
            category: newFile.category,
            downloadLink: downloadLink,
            qrCode: qrCode,
            thumbnail: thumbnail,
          });
        } catch (fileError) {
          console.error(
            `Error processing file ${file.originalname}:`,
            fileError
          );
          errors.push({
            filename: file.originalname,
            error: fileError.message,
          });

          // Clean up file if processing failed
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }
        }
      }

      // No user statistics to update since no authentication

      // Create ZIP file if requested (UNIQUE FEATURE)
      let zipDownloadLink = null;
      if (createZip === "true" && uploadedFiles.length > 1) {
        try {
          const zipFileId = uuidv4();
          const zipFilename = `bulk_download_${Date.now()}.zip`;
          const zipPath = path.join("uploads/files", zipFilename);

          const output = fs.createWriteStream(zipPath);
          const archive = archiver("zip", { zlib: { level: 9 } });

          archive.pipe(output);

          // Add all uploaded files to ZIP
          for (const uploadedFile of uploadedFiles) {
            const file = await File.findOne({ fileId: uploadedFile.id });
            if (file && fs.existsSync(file.filePath)) {
              archive.file(file.filePath, { name: file.originalName });
            }
          }

          await archive.finalize();

          // Create ZIP file record
          const zipFile = new File({
            fileId: zipFileId,
            owner: null, // No authentication, so no owner
            originalName: zipFilename,
            filename: zipFilename,
            filePath: zipPath,
            size: fs.statSync(zipPath).size,
            mimetype: "application/zip",
            description: `Bulk download archive containing ${uploadedFiles.length} files`,
            batchId: batchId,
            category: "archive",
          });

          await zipFile.save();
          zipDownloadLink = `${req.protocol}://${req.get(
            "host"
          )}/api/download/${zipFileId}`;
        } catch (zipError) {
          console.error("ZIP creation error:", zipError);
        }
      }

      // Generate batch download page link (UNIQUE FEATURE)
      const batchDownloadLink = `${req.protocol}://${req.get(
        "host"
      )}/batch/${batchId}`;

      // Generate selective download link (NEW SAAS FEATURE)
      const selectiveDownloadLink = `${req.protocol}://${req.get(
        "host"
      )}/select/${batchId}`;

      res.status(200).json({
        success: true,
        message: `Successfully uploaded ${uploadedFiles.length} files`,
        batchId: batchId,
        batchDownloadLink: batchDownloadLink,
        selectiveDownloadLink: selectiveDownloadLink,
        zipDownloadLink: zipDownloadLink,
        files: uploadedFiles,
        errors: errors,
        summary: {
          totalFiles: uploadedFiles.length,
          totalSize: uploadedFiles.reduce((sum, file) => sum + file.size, 0),
          failedFiles: errors.length,
        },
      });
    } catch (error) {
      console.error("Bulk upload error:", error);

      // Clean up uploaded files if batch processing failed
      if (req.files) {
        req.files.forEach((file) => {
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
          }
        });
      }

      res.status(500).json({
        success: false,
        message: "Server error during bulk upload",
        error: error.message,
      });
    }
  }
);

module.exports = router;
