import { useState, useEffect } from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { Toaster } from "react-hot-toast";
import { motion, AnimatePresence } from "framer-motion";

// Components
import Navbar from "./components/Navbar";
import Home from "./pages/Home";
import Login from "./pages/Login";
import Register from "./pages/Register";
import Dashboard from "./pages/Dashboard";
import BulkUpload from "./pages/BulkUpload";
import BatchDownload from "./pages/BatchDownload";
import SelectiveDownload from "./pages/SelectiveDownload";
import Download from "./pages/Download";

// Context
import { AuthProvider, useAuth } from "./context/AuthContext";

// Styles
import "./App.css";

// Protected Route Component
const ProtectedRoute = ({ children }) => {
  const { user, loading } = useAuth();

  if (loading) {
    return (
      <div className="loading-screen">
        <div className="spinner-large"></div>
        <p>Loading...</p>
      </div>
    );
  }

  return user ? children : <Navigate to="/login" />;
};

// Main App Component
function AppContent() {
  const { user, loading } = useAuth();
  const [theme, setTheme] = useState("auto");

  // Theme management
  useEffect(() => {
    const savedTheme = localStorage.getItem("quickshare-theme") || "auto";
    setTheme(savedTheme);
    applyTheme(savedTheme);
  }, []);

  const applyTheme = (selectedTheme) => {
    const root = document.documentElement;

    if (selectedTheme === "auto") {
      const prefersDark = window.matchMedia(
        "(prefers-color-scheme: dark)"
      ).matches;
      root.setAttribute("data-theme", prefersDark ? "dark" : "light");
    } else {
      root.setAttribute("data-theme", selectedTheme);
    }
  };

  const toggleTheme = () => {
    const themes = ["light", "dark", "auto"];
    const currentIndex = themes.indexOf(theme);
    const nextTheme = themes[(currentIndex + 1) % themes.length];

    setTheme(nextTheme);
    localStorage.setItem("quickshare-theme", nextTheme);
    applyTheme(nextTheme);
  };

  if (loading) {
    return (
      <div className="loading-screen">
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
          className="loading-content"
        >
          <div className="spinner-large"></div>
          <h2>QuickShare</h2>
          <p>Loading your experience...</p>
        </motion.div>
      </div>
    );
  }

  return (
    <div className="app">
      <Navbar theme={theme} onThemeToggle={toggleTheme} />

      <AnimatePresence mode="wait">
        <Routes>
          <Route path="/" element={<Home />} />
          <Route
            path="/login"
            element={user ? <Navigate to="/dashboard" /> : <Login />}
          />
          <Route
            path="/register"
            element={user ? <Navigate to="/dashboard" /> : <Register />}
          />
          <Route
            path="/dashboard"
            element={
              <ProtectedRoute>
                <Dashboard />
              </ProtectedRoute>
            }
          />
          <Route path="/bulk-upload" element={<BulkUpload />} />
          <Route path="/batch/:batchId" element={<BatchDownload />} />
          <Route path="/select/:batchId" element={<SelectiveDownload />} />
          <Route path="/download/:id" element={<Download />} />
          <Route path="*" element={<Navigate to="/" />} />
        </Routes>
      </AnimatePresence>

      <footer className="app-footer">
        <div className="footer-content">
          <div className="footer-section">
            <h3>📁 QuickShare</h3>
            <p>The most advanced file sharing platform</p>
          </div>
          <div className="footer-section">
            <h4>Features</h4>
            <ul>
              <li>Bulk Upload</li>
              <li>QR Code Sharing</li>
              <li>Auto-Generated ZIP</li>
              <li>User Accounts</li>
              <li>File Analytics</li>
            </ul>
          </div>
          <div className="footer-section">
            <h4>Limits</h4>
            <ul>
              <li>Guest: 100MB per file</li>
              <li>Registered: 500MB per file</li>
              <li>Premium: 5GB per file</li>
              <li>Bulk: Up to 20 files</li>
            </ul>
          </div>
          <div className="footer-section">
            <h4>Security</h4>
            <ul>
              <li>End-to-End Encryption</li>
              <li>Virus Scanning</li>
              <li>Auto-Expiration</li>
              <li>Password Protection</li>
            </ul>
          </div>
        </div>
        <div className="footer-bottom">
          <p>Built with ❤️ using MERN Stack | © 2024 QuickShare</p>
        </div>
      </footer>

      <Toaster
        position="top-right"
        toastOptions={{
          duration: 4000,
          style: {
            background: "var(--card-bg)",
            color: "var(--text-primary)",
            border: "1px solid var(--border-color)",
          },
        }}
      />
    </div>
  );
}

// Main App with Router and Context
function App() {
  return (
    <Router>
      <AuthProvider>
        <AppContent />
      </AuthProvider>
    </Router>
  );
}

export default App;
