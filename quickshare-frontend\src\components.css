/* Component-Specific Styles for QuickShare */

/* File Upload Component */
.file-upload {
  width: 100%;
}

.upload-area {
  border: 2px dashed var(--border-color);
  border-radius: 1rem;
  padding: 3rem 2rem;
  text-align: center;
  background: var(--bg-secondary);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.upload-area::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  opacity: 0;
  transition: opacity 0.3s ease;
}

.upload-area:hover::before,
.upload-area.drag-active::before {
  opacity: 0.05;
}

.upload-area:hover,
.upload-area.drag-active {
  border-color: var(--accent-primary);
  background: var(--card-bg);
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
}

.upload-content {
  position: relative;
  z-index: 2;
}

.upload-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--accent-primary);
  animation: float 3s ease-in-out infinite;
}

.upload-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.upload-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0.25rem 0;
}

.progress-container {
  margin-top: 1.5rem;
  padding: 1rem;
  background: var(--card-bg);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

.progress-container p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: center;
  margin: 0;
}

.file-upload-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--accent-primary);
  animation: float 3s ease-in-out infinite;
}

.file-upload-text {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.file-upload-subtext {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin-bottom: 1.5rem;
}

.file-upload-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.file-upload-button:hover {
  background: var(--accent-secondary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.file-upload-progress {
  margin-top: 1rem;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--bg-tertiary);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(
    90deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  border-radius: 4px;
  transition: width 0.3s ease;
  position: relative;
}

.progress-fill::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  animation: shimmer 2s infinite;
}

.progress-text {
  font-size: 0.875rem;
  color: var(--text-secondary);
  text-align: center;
}

/* Share Link Component */
.share-link {
  text-align: center;
  padding: 2rem;
  background: var(--card-bg);
  border-radius: 1.5rem;
  border: 1px solid var(--border-color);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.success-message {
  margin-bottom: 2rem;
}

.success-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
  animation: bounceIn 0.8s ease-out;
}

.success-message h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.file-info {
  background: var(--bg-secondary);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid var(--border-color);
  text-align: left;
}

.file-details {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.file-icon {
  font-size: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: var(--card-bg);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
}

.file-meta h3 {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 1.125rem;
}

.file-meta p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0.25rem 0;
}

.download-link-section {
  margin-bottom: 2rem;
}

.download-link-section h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.link-container {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.link-input {
  flex: 1;
  padding: 0.875rem 1rem;
  border: 2px solid var(--border-color);
  border-radius: 0.75rem;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 0.875rem;
  outline: none;
  transition: all 0.3s ease;
}

.link-input:focus {
  border-color: var(--accent-primary);
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.copy-button {
  padding: 0.875rem 1.25rem;
  background: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 0.75rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.copy-button:hover {
  background: var(--accent-secondary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.copy-button.copied {
  background: var(--success);
}

.link-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 2rem;
}

.test-button,
.upload-another-button {
  padding: 0.75rem 1.5rem;
  border-radius: 0.75rem;
  font-weight: 600;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
}

.test-button {
  background: var(--accent-primary);
  color: white;
}

.test-button:hover {
  background: var(--accent-secondary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.upload-another-button {
  background: var(--bg-secondary);
  color: var(--text-primary);
  border: 2px solid var(--border-color);
}

.upload-another-button:hover {
  border-color: var(--accent-primary);
  background: rgba(102, 126, 234, 0.05);
  transform: translateY(-2px);
}

.security-note {
  background: rgba(251, 191, 36, 0.1);
  border: 1px solid rgba(251, 191, 36, 0.3);
  border-radius: 0.75rem;
  padding: 1rem;
  margin-top: 1rem;
}

.security-note p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.security-note strong {
  color: var(--warning);
}

.share-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
}

.share-option {
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.share-option:hover {
  border-color: var(--accent-primary);
  background: rgba(102, 126, 234, 0.05);
  transform: translateY(-2px);
}

.share-option-icon {
  font-size: 2rem;
  margin-bottom: 0.5rem;
  display: block;
}

.share-option-title {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
}

.share-option-desc {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.link-input-group {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.link-input {
  flex: 1;
  padding: 0.75rem 1rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background: var(--bg-secondary);
  color: var(--text-primary);
  font-family: "Monaco", "Menlo", "Ubuntu Mono", monospace;
  font-size: 0.875rem;
}

.copy-button {
  padding: 0.75rem 1rem;
  background: var(--accent-primary);
  color: white;
  border: none;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.copy-button:hover {
  background: var(--accent-secondary);
}

.copy-button.copied {
  background: var(--success);
}

.qr-code-section {
  background: var(--bg-secondary);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-top: 1.5rem;
  border: 1px solid var(--border-color);
  text-align: center;
}

.qr-code-section h4 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.qr-code-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
}

.qr-code-image {
  max-width: 200px;
  height: auto;
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
  background: white;
  padding: 0.5rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.qr-description {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.qr-code {
  border-radius: 0.5rem;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Bulk Upload Specific Styles */
.bulk-upload-page {
  padding: 2rem 0;
}

.bulk-upload-header {
  text-align: center;
  margin-bottom: 3rem;
}

.bulk-upload-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.bulk-upload-header p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
}

.bulk-upload-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 1rem;
}

.upload-zone {
  background: var(--card-bg);
  border-radius: 1.5rem;
  padding: 2rem;
  border: 1px solid var(--border-color);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

.dropzone {
  border: 2px dashed var(--border-color);
  border-radius: 1rem;
  padding: 3rem 2rem;
  text-align: center;
  background: var(--bg-secondary);
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}

.dropzone.active {
  border-color: var(--accent-primary);
  background: rgba(102, 126, 234, 0.05);
  transform: scale(1.02);
}

.dropzone.has-files {
  border-style: solid;
  background: var(--card-bg);
}

.dropzone-content {
  position: relative;
  z-index: 2;
}

.dropzone-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: var(--accent-primary);
  animation: float 3s ease-in-out infinite;
}

.dropzone-content h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
}

.dropzone-content p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.files-list {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.files-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.files-header h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 1rem;
}

.file-item {
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  padding: 1rem;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
}

.file-item:hover {
  border-color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.file-item .file-icon {
  font-size: 2.5rem;
  margin-bottom: 0.75rem;
  display: block;
}

.file-item .file-info h4 {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
  font-size: 0.875rem;
  word-break: break-word;
}

.file-item .file-info p {
  font-size: 0.75rem;
  color: var(--text-secondary);
  margin: 0;
}

.file-progress {
  margin-top: 0.75rem;
}

.file-progress .progress-bar {
  height: 4px;
  margin-bottom: 0.25rem;
}

.file-progress span {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

.file-status {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
}

.file-status.success {
  color: var(--success);
  background: rgba(16, 185, 129, 0.1);
}

.file-status.error {
  color: var(--error);
  background: rgba(239, 68, 68, 0.1);
}

.remove-file {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: var(--error);
  color: white;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  transition: all 0.3s ease;
}

.remove-file:hover {
  background: #dc2626;
  transform: scale(1.1);
}

.upload-options {
  margin-top: 2rem;
  padding-top: 2rem;
  border-top: 1px solid var(--border-color);
}

.upload-options h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
}

.options-grid {
  display: grid;
  gap: 1rem;
  margin-bottom: 2rem;
}

.option-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
}

.option-item label {
  font-weight: 500;
  color: var(--text-primary);
  cursor: pointer;
  flex: 1;
}

.option-item input[type="checkbox"] {
  width: 1.25rem;
  height: 1.25rem;
  accent-color: var(--accent-primary);
}

.option-item select,
.option-item input[type="text"] {
  flex: 1;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--border-color);
  border-radius: 0.5rem;
  background: var(--card-bg);
  color: var(--text-primary);
  font-size: 0.875rem;
}

.btn-large {
  padding: 1rem 2rem;
  font-size: 1.125rem;
  font-weight: 600;
}

.btn-small {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

/* Upload Success Styles */
.upload-success {
  text-align: center;
  padding: 3rem 2rem;
}

.success-content {
  max-width: 800px;
  margin: 0 auto;
}

.success-content .success-icon {
  font-size: 5rem;
  margin-bottom: 1.5rem;
  animation: bounceIn 0.8s ease-out;
}

.success-content h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.success-content p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.upload-summary {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 1.5rem;
  margin-bottom: 3rem;
}

.summary-stat {
  text-align: center;
  padding: 1.5rem;
  background: var(--bg-secondary);
  border-radius: 1rem;
  border: 1px solid var(--border-color);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: 700;
  color: var(--accent-primary);
  margin-bottom: 0.5rem;
}

.stat-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
  font-weight: 500;
}

.download-options {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
  margin-bottom: 3rem;
}

.individual-files {
  margin-bottom: 3rem;
}

.individual-files h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 1.5rem;
  text-align: center;
}

.file-result-card {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--bg-secondary);
  border: 1px solid var(--border-color);
  border-radius: 0.75rem;
  transition: all 0.3s ease;
}

.file-result-card:hover {
  border-color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.file-result-card .file-icon {
  font-size: 2rem;
  width: 50px;
  height: 50px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--card-bg);
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
}

.file-result-card .file-info {
  flex: 1;
  text-align: left;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
}

.file-result-card .file-info h4 {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.25rem;
  font-size: 1rem;
}

.file-result-card .file-info p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0;
}

.file-actions {
  display: flex;
  gap: 0.5rem;
}

/* Utility Classes */
.text-gradient {
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

.hover-lift {
  transition: transform 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-4px);
}

.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: fadeInUp 0.6s ease-out;
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

/* Download Page Styles */
.download-page {
  padding: 2rem 0;
  min-height: 100vh;
}

.download-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0 1rem;
}

.download-header {
  text-align: center;
  margin-bottom: 3rem;
}

.download-header h1 {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  background: linear-gradient(
    135deg,
    var(--accent-primary),
    var(--accent-secondary)
  );
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.download-header p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin: 0;
}

.loading-container,
.error-container {
  text-align: center;
  padding: 4rem 2rem;
}

.error-icon {
  font-size: 4rem;
  margin-bottom: 1.5rem;
}

.error-container h2 {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
}

.error-container p {
  font-size: 1.125rem;
  color: var(--text-secondary);
  margin-bottom: 2rem;
}

.file-preview-card {
  background: var(--card-bg);
  border: 2px solid var(--border-color);
  border-radius: 1rem;
  padding: 1.5rem;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
}

.file-preview-card:hover {
  border-color: var(--accent-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.file-preview-card.selected {
  border-color: var(--accent-primary);
  background: rgba(102, 126, 234, 0.05);
}

.file-preview-card.large {
  cursor: default;
  text-align: center;
  padding: 3rem 2rem;
}

.file-preview-header {
  display: flex;
  align-items: center;
  gap: 2rem;
  margin-bottom: 2rem;
  text-align: left;
}

.file-icon {
  font-size: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  background: var(--bg-secondary);
  border-radius: 0.75rem;
  border: 1px solid var(--border-color);
}

.file-icon.large {
  font-size: 4rem;
  width: 100px;
  height: 100px;
  border-radius: 1rem;
}

.file-details h2 {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--text-primary);
  margin-bottom: 1rem;
  word-break: break-word;
}

.file-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

.file-meta span {
  font-size: 0.875rem;
  color: var(--text-secondary);
  padding: 0.25rem 0.75rem;
  background: var(--bg-secondary);
  border-radius: 0.5rem;
  border: 1px solid var(--border-color);
}

.file-description {
  background: var(--bg-secondary);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid var(--border-color);
  text-align: left;
}

.file-description h3 {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.file-description p {
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.6;
}

.download-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.batch-description {
  background: var(--bg-secondary);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  border: 1px solid var(--border-color);
  text-align: center;
}

.batch-description h3 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.75rem;
}

.batch-description p {
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.6;
}

.selection-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--card-bg);
  border: 1px solid var(--border-color);
  border-radius: 1rem;
  padding: 1.5rem;
  margin-bottom: 2rem;
  flex-wrap: wrap;
  gap: 1rem;
}

.selection-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.selection-info span {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.selection-info span:first-child {
  font-weight: 600;
  color: var(--text-primary);
  font-size: 1rem;
}

.selection-actions {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.files-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 1.5rem;
}

.file-checkbox {
  position: absolute;
  top: 1rem;
  left: 1rem;
  z-index: 10;
}

.file-checkbox input[type="checkbox"] {
  width: 1.25rem;
  height: 1.25rem;
  accent-color: var(--accent-primary);
  cursor: pointer;
}

.file-preview-card .file-info {
  text-align: center;
  margin: 1rem 0;
}

.file-preview-card .file-info h4 {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 0.5rem;
  font-size: 1rem;
  word-break: break-word;
}

.file-preview-card .file-info p {
  font-size: 0.875rem;
  color: var(--text-secondary);
  margin: 0.25rem 0;
}

.file-preview-card .file-actions {
  display: flex;
  justify-content: center;
  margin-top: 1rem;
}

/* Responsive adjustments for components */
@media (max-width: 768px) {
  .share-options {
    grid-template-columns: 1fr;
  }

  .bulk-upload-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  }

  .link-input-group {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
    align-items: center;
  }

  .file-preview-header {
    flex-direction: column;
    text-align: center;
    gap: 1rem;
  }

  .selection-controls {
    flex-direction: column;
    align-items: stretch;
  }

  .selection-actions {
    justify-content: center;
  }

  .files-grid {
    grid-template-columns: 1fr;
  }

  .download-actions {
    flex-direction: column;
    align-items: center;
  }
}

@media (max-width: 480px) {
  .file-upload-dropzone {
    padding: 2rem 1rem;
  }

  .share-link-container {
    padding: 1.5rem;
  }

  .bulk-upload-grid {
    grid-template-columns: 1fr 1fr;
  }

  .download-container {
    padding: 0 0.75rem;
  }

  .file-preview-card {
    padding: 1rem;
  }

  .file-preview-card.large {
    padding: 2rem 1rem;
  }

  .file-meta {
    flex-direction: column;
    gap: 0.5rem;
  }

  .selection-actions {
    flex-direction: column;
  }
}
