import { useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { motion } from "framer-motion";
import { useAuth } from "../context/AuthContext";

const Navbar = ({ theme, onThemeToggle }) => {
  const { user, logout, getStorageInfo } = useAuth();
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();

  const storageInfo = getStorageInfo();

  const handleLogout = async () => {
    await logout();
    navigate("/");
    setIsProfileOpen(false);
  };

  const getThemeIcon = () => {
    switch (theme) {
      case "light":
        return "☀️";
      case "dark":
        return "🌙";
      default:
        return "🌓";
    }
  };

  const isActive = (path) => location.pathname === path;

  return (
    <nav className="navbar">
      <div className="navbar-container">
        {/* Logo */}
        <Link to="/" className="navbar-logo">
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            className="logo-content"
          >
            <span className="logo-icon">📁</span>
            <span className="logo-text">QuickShare</span>
          </motion.div>
        </Link>

        {/* Desktop Navigation */}
        <div className="navbar-menu">
          <Link
            to="/"
            className={`navbar-link ${isActive("/") ? "active" : ""}`}
          >
            🏠 Home
          </Link>

          <Link
            to="/bulk-upload"
            className={`navbar-link ${
              isActive("/bulk-upload") ? "active" : ""
            }`}
          >
            📦 Bulk Upload
          </Link>
        </div>

        {/* Right Side */}
        <div className="navbar-actions">
          {/* Theme Toggle */}
          <motion.button
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
            onClick={onThemeToggle}
            className="theme-toggle"
            title={`Current theme: ${theme}`}
          >
            {getThemeIcon()}
          </motion.button>

          {/* User Menu */}
          {user ? (
            <div className="user-menu">
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                className="user-button"
              >
                <div className="user-avatar">
                  {user.displayName.charAt(0).toUpperCase()}
                </div>
                <div className="user-info">
                  <span className="user-name">{user.displayName}</span>
                  {user.isPremium && (
                    <span className="premium-badge">✨ Premium</span>
                  )}
                </div>
                <span className="dropdown-arrow">▼</span>
              </motion.button>

              {isProfileOpen && (
                <motion.div
                  initial={{ opacity: 0, y: -10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="user-dropdown"
                >
                  <div className="dropdown-header">
                    <div className="user-details">
                      <strong>{user.displayName}</strong>
                      <span>{user.email}</span>
                    </div>
                    {storageInfo && (
                      <div className="storage-info">
                        <div className="storage-bar">
                          <div
                            className="storage-fill"
                            style={{ width: `${storageInfo.percentage}%` }}
                          ></div>
                        </div>
                        <span className="storage-text">
                          {storageInfo.percentage}% used (
                          {user.formatFileSize?.(storageInfo.used)} /{" "}
                          {user.formatFileSize?.(storageInfo.limit)})
                        </span>
                      </div>
                    )}
                  </div>

                  <div className="dropdown-menu">
                    <Link to="/dashboard" className="dropdown-item">
                      📊 Dashboard
                    </Link>
                    <Link to="/bulk-upload" className="dropdown-item">
                      📦 Bulk Upload
                    </Link>
                    {!user.isPremium && (
                      <button className="dropdown-item premium-upgrade">
                        ✨ Upgrade to Premium
                      </button>
                    )}
                    <hr className="dropdown-divider" />
                    <button
                      onClick={handleLogout}
                      className="dropdown-item logout"
                    >
                      🚪 Logout
                    </button>
                  </div>
                </motion.div>
              )}
            </div>
          ) : (
            <div className="auth-buttons">
              <Link to="/login" className="btn btn-outline">
                Login
              </Link>
              <Link to="/register" className="btn btn-primary">
                Sign Up
              </Link>
            </div>
          )}

          {/* Mobile Menu Toggle */}
          <button
            className="mobile-menu-toggle"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <span></span>
            <span></span>
            <span></span>
          </button>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: "auto" }}
          exit={{ opacity: 0, height: 0 }}
          className="mobile-menu"
        >
          <Link
            to="/"
            className="mobile-link"
            onClick={() => setIsMenuOpen(false)}
          >
            🏠 Home
          </Link>
          <Link
            to="/bulk-upload"
            className="mobile-link"
            onClick={() => setIsMenuOpen(false)}
          >
            📦 Bulk Upload
          </Link>
          {user ? (
            <>
              <Link
                to="/dashboard"
                className="mobile-link"
                onClick={() => setIsMenuOpen(false)}
              >
                📊 Dashboard
              </Link>
              <button onClick={handleLogout} className="mobile-link logout">
                🚪 Logout
              </button>
            </>
          ) : (
            <>
              <Link
                to="/login"
                className="mobile-link"
                onClick={() => setIsMenuOpen(false)}
              >
                Login
              </Link>
              <Link
                to="/register"
                className="mobile-link"
                onClick={() => setIsMenuOpen(false)}
              >
                Sign Up
              </Link>
            </>
          )}
        </motion.div>
      )}
    </nav>
  );
};

export default Navbar;
