import { useState } from "react";

const ShareLink = ({ fileData, onReset }) => {
  const [copied, setCopied] = useState(false);

  // Format file size
  const formatFileSize = (bytes) => {
    if (bytes === 0) return "0 Bytes";
    const k = 1024;
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
  };

  // Copy link to clipboard
  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(fileData.downloadLink);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000); // Reset after 2 seconds
    } catch (err) {
      console.error("Failed to copy: ", err);
      // Fallback for older browsers
      const textArea = document.createElement("textarea");
      textArea.value = fileData.downloadLink;
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      try {
        document.execCommand("copy");
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      } catch (err) {
        console.error("Fallback copy failed: ", err);
      }
      document.body.removeChild(textArea);
    }
  };

  // Get file type icon based on mimetype
  const getFileIcon = (mimetype) => {
    if (mimetype.startsWith("image/")) return "🖼️";
    if (mimetype.startsWith("video/")) return "🎥";
    if (mimetype.startsWith("audio/")) return "🎵";
    if (mimetype.includes("pdf")) return "📄";
    if (mimetype.includes("word") || mimetype.includes("document")) return "📝";
    if (mimetype.includes("excel") || mimetype.includes("spreadsheet"))
      return "📊";
    if (mimetype.includes("powerpoint") || mimetype.includes("presentation"))
      return "📈";
    if (
      mimetype.includes("zip") ||
      mimetype.includes("rar") ||
      mimetype.includes("archive")
    )
      return "📦";
    return "📁";
  };

  return (
    <div className="share-link">
      <div className="success-message">
        <div className="success-icon">✅</div>
        <h2>File Uploaded Successfully!</h2>
      </div>

      <div className="file-info">
        <div className="file-details">
          <div className="file-icon">{getFileIcon(fileData.mimetype)}</div>
          <div className="file-meta">
            <h3>{fileData.originalName}</h3>
            <p>Size: {formatFileSize(fileData.size)}</p>
            <p>Type: {fileData.mimetype}</p>
          </div>
        </div>
      </div>

      <div className="download-link-section">
        <h3>Share this link:</h3>
        <div className="link-container">
          <input
            type="text"
            value={fileData.downloadLink}
            readOnly
            className="link-input"
          />
          <button
            onClick={copyToClipboard}
            className={`copy-button ${copied ? "copied" : ""}`}
          >
            {copied ? "✅ Copied!" : "📋 Copy"}
          </button>
        </div>
        <p className="link-description">
          Anyone with this link can download your file directly
        </p>

        {/* QR Code Section */}
        {fileData.qrCode && (
          <div className="qr-code-section">
            <h4>📱 QR Code for Mobile Sharing</h4>
            <div className="qr-code-container">
              <img
                src={fileData.qrCode}
                alt="QR Code for download link"
                className="qr-code-image"
              />
              <p className="qr-description">
                Scan with your phone to share instantly
              </p>
            </div>
          </div>
        )}
      </div>

      <div className="action-buttons">
        <button onClick={onReset} className="upload-another-button">
          📁 Upload Another File
        </button>
      </div>

      <div className="security-note">
        <p>
          <strong>Security Note:</strong> Keep your download link secure. Anyone
          with this link can access your file.
        </p>
      </div>
    </div>
  );
};

export default ShareLink;
