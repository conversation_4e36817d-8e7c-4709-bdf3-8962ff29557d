/* Tailwind CSS imports */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* CSS Variables for theming */
:root {
  /* Light theme colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --text-primary: #1e293b;
  --text-secondary: #64748b;
  --text-tertiary: #94a3b8;
  --border-color: #e2e8f0;
  --card-bg: #ffffff;
  --navbar-bg: rgba(255, 255, 255, 0.95);
  --hero-bg: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --accent-primary: #667eea;
  --accent-secondary: #764ba2;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;
  --info: #3b82f6;

  /* Font settings */
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Dark theme colors */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --text-primary: #f1f5f9;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;
  --border-color: #334155;
  --card-bg: #1e293b;
  --navbar-bg: rgba(15, 23, 42, 0.95);
  --hero-bg: linear-gradient(135deg, #1e293b 0%, #0f172a 100%);
  --accent-primary: #60a5fa;
  --accent-secondary: #a78bfa;
}
